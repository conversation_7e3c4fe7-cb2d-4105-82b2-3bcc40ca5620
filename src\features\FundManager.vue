n<template>
  <div class="feature-container">
    <div class="feature-header">
      <h1 class="title">资金协管</h1>
      <div class="header-actions">
        <el-button type="primary" size="small">
          <el-icon>
            <Document />
          </el-icon>
          操作手册
        </el-button>
      </div>
    </div>

    <div class="feature-content">
      <div class="feature-description">
        <p>智能化资金管理专家</p>
      </div>

      <div class="feature-modules">
        <div class="module-card" @click="goTo('/fund-manager/flow')">
          <div class="module-icon">
            <div class="icon-wrapper">
              <el-icon>
                <Wallet />
              </el-icon>
            </div>
          </div>
          <div class="module-info">
            <h3>资金流水</h3>
            <p>查看资金流动情况</p>
          </div>
        </div>

        <div class="module-card" @click="goTo('/fund-manager/budget')">
          <div class="module-icon">
            <div class="icon-wrapper">
              <el-icon>
                <Coin />
              </el-icon>
            </div>
          </div>
          <div class="module-info">
            <h3>预算管理</h3>
            <p>制定和跟踪预算</p>
          </div>
        </div>

        <div class="module-card" @click="goTo('/fund-manager/analysis')">
          <div class="module-icon">
            <div class="icon-wrapper">
              <el-icon>
                <DataAnalysis />
              </el-icon>
            </div>
          </div>
          <div class="module-info">
            <h3>资金分析</h3>
            <p>分析资金使用情况</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goTo = (path) => {
  router.push(path)
}
</script>

<style scoped>
.feature-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  width: 100%;
  box-sizing: border-box;
}

.feature-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 0 10px;
  width: 100%;
  max-width: 1200px;
}

.title {
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.feature-content {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-description {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
  margin-bottom: 30px;
  text-align: center;
}

.feature-description p {
  font-size: 16px;
  color: #6c757d;
  margin: 0;
}

.feature-modules {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  justify-items: center;
  place-content: center;
}

.module-card {
  background: white;
  border-radius: 16px;
  padding: 24px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);
  width: 100%;
  min-width: 160px;
  max-width: 180px;
  position: relative;
  overflow: hidden;
}

.module-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.module-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
}

.module-card:hover::before {
  opacity: 1;
}

.module-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
}

.module-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.icon-wrapper {
  font-size: 26px;
  color: white;
  z-index: 2;
  position: relative;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.module-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.module-icon:hover::before {
  opacity: 1;
}

.module-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #2c3e50;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.module-info p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
  line-height: 1.5;
  font-weight: 400;
}
</style>